"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";
import DeleteConfirm from "@/components/delete-dailog";
import { useAuth } from "@/context/AuthContext";
import { SquarePen, Trash2 } from "@/components/icons/list";

export interface group {
  id: string;
  name: string;
  new_group_name?: string;
}

// group Table Component
export function GroupTable({
  // onDelete,
  handleEdit,
  groups,
}: {
  // onDelete: (name: string) => void;
  handleEdit: (group: group) => void;
  groups: group[];
}) {
  const [selectedGroup, setSelectedGroup] = useState<group | null>(null);
  const [isDeleteGroup, setIsDeleteGroup] = useState(false);
  const { isRefreshed, setIsRefreshed } = useAuth();

  const handleDeleteGroup = (group: group) => {
    setSelectedGroup(group);
    setIsDeleteGroup(true);
  };
  const handleDelete = async (name: string) => {
    try {
      await apiClient.delete(`/group/${name}`);
      toast.success("Group deleted successfully!"); // Changed "Department" to "Group" for consistency
      console.log("Group deleted successfully");
      setIsDeleteGroup(false);
      setSelectedGroup(null); // Clear selected group after deletion
      setIsRefreshed((prev) => !prev);
    } catch (err: any) {
      console.error("Failed to delete group:", err);
      let errorMessage = "Failed to delete group."; // Default error message
      if (err.response && err.response.data && err.response.data.error) {
        errorMessage = err.response.data.error;
      } else if (err.message) {
        errorMessage = err.message; // Fallback for network errors or other JS errors
      }
      toast.error(errorMessage);
    }
  };

  return (
    <div className="bg-white rounded shadow p-4 overflow-x-auto">
      <div className="w-full overflow-x-auto">
        <table className="w-full table-fixed">
          <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
            <tr>
              <th className="px-4 py-2 w-1/3">S.N.</th>
              <th className="px-4 py-2 w-1/3">Group Name</th>
              <th className="px-4 py-2 text-end w-1/3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {groups?.length === 0 ? (
              <tr>
                <td
                  colSpan={3}
                  className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                >
                  Oops! No Group matched your search
                </td>
              </tr>
            ) : (
              groups?.map((group, index) => (
                <tr key={group?.id} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-1 text-xs">{index + 1}</td>
                  <td className="px-4 py-1 text-xs truncate">{group?.name}</td>
                  <td className="px-4 py-1 text-xs">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        onClick={() => {
                          handleEdit(group);
                        }}
                        className="bg-green-500 hover:bg-green-600 text-white p-1 rounded h-7 w-7"
                      >
                        <SquarePen className="h-4 w-4" />
                      </Button>

                      <Button
                        onClick={() => handleDeleteGroup(group)}
                        className="bg-red-500 hover:bg-red-600 text-white p-1 rounded h-7 w-7"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      {isDeleteGroup && selectedGroup && (
        <DeleteConfirm
          name={selectedGroup.name}
          id={0}
          paraValue="Group"
          value={selectedGroup.name}
          onClose={() => setIsDeleteGroup(false)}
          onDelete={(_id: number, nameFromDialog: string) => {
            handleDelete(nameFromDialog);
          }}
          loading={false}
        ></DeleteConfirm>
      )}
    </div>
  );
}
export function AddGroupForm({
  onClose,
  onSubmit,
}: {
  onSubmit: (groupname: group) => void;
  onClose: () => void;
}) {
  const [newgroup, setNewgroup] = useState({
    groupname: "",
  });
  const isformValid = newgroup?.groupname.trim() !== "";

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6">
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
          <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
            <h1 className="text-lg font-extrabold text-center">
              Add New Group
            </h1>
            <p className="text-blue-100 text-sm text-center">
              Create a new group with the required information
            </p>
          </div>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              onSubmit({
                // id: Date.now().toString(), // Ensure id is a string
                groupname: newgroup?.groupname.trim(), // Changed groupname to name to match interface
              });
              setNewgroup({
                groupname: "",
              });
            }}
            className="flex-grow p-5 grid grid-cols-1 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar"
          >
            <Input
              placeholder="Enter Group Name"
              value={newgroup?.groupname}
              onChange={(e) =>
                setNewgroup((prev) => ({ ...prev, groupname: e.target.value }))
              }
            />
            <div className="space-y-2">
              <Button
                type="submit"
                className="w-full bg-primary"
                disabled={!isformValid}
              >
                Add group
              </Button>
              <Button variant="outline" onClick={onClose} className="w-full">
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
