// components/settings/users/user-management.tsx
"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SquarePen } from "@/components/icons/list"; // Assuming this path is correct
import { DeleteUserDialog } from "@/app/app/settings/users/page";

export type UserGroup = string;
export type UserOrganization = string;

export interface User {
  id: string;
  username: string;
  email: string;
  group: UserGroup;
  password: string;
  status: string;
  organization: UserOrganization;
}
// User Table Component
export function UserTable({
  users,
  onEdit,
  onDelete,
}: {
  users: User[];
  onDelete: (id: string) => void;
  onEdit: (user: User) => void;
}) {
  const sortedUsers = [...users].sort((a, b) =>
    a.username.localeCompare(b.username)
  );
  // const router = useRouter();
  return (
    // Added overflow-x-auto for horizontal scrolling on small screens
    <div className="bg-white rounded shadow p-4 overflow-x-auto">
      <div className="w-full overflow-x-auto ">
        <table className="min-w-max w-full">
          <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
            <tr>
              <th className="px-4 py-2 ">S.N.</th>
              <th className="px-4 py-2 ">Username</th>
              <th className="px-4 py-2 ">Email</th>
              <th className="px-4 py-2 ">Status</th>
              <th className="px-4 py-2 ">Group</th>
              <th className="px-4 py-2 text-end">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedUsers?.length === 0 ? (
              <tr>
                <td
                  colSpan={7}
                  className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                >
                  Oops! No user matched your search
                </td>
              </tr>
            ) : (
              sortedUsers?.map((user, index) => (
                <tr
                  key={user?.id}
                  id={user?.id}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="px-4 py-1 text-xs">{index + 1}</td>
                  <td className="px-4 py-1 text-xs font-black">
                    {user?.username}
                  </td>
                  <td className="px-4 py-1 text-xs">{user?.email}</td>
                  <td className="px-4 py-1 text-xs">
                    <StatusBadge status={user?.status} />
                  </td>
                  <td className="px-4 py-1 text-xs">
                    <GroupBadge group={user?.group} />
                  </td>
                  <td className="px-4 py-1 text-xs">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        onClick={() => {
                          // sessionStorage.setItem("editUser", JSON.stringify(user));
                          // router.push(`/app/settings/users/edit/${user.id}`);
                          onEdit?.(user);
                        }}
                        className="bg-green-500 hover:bg-green-600 text-white p-1 rounded h-7 w-7"
                      >
                        <SquarePen className="h-4 w-4" />
                      </Button>
                      <DeleteUserDialog
                        username={user?.username}
                        onDelete={() => onDelete?.(user?.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
// Shared Components
function GroupBadge({ group }: { group: UserGroup }) {
  const getGroupColor = (groupName: string) => {
    // Define specific colors for key roles
    switch (groupName.toLowerCase()) {
      case "superadmin":
        return "bg-red-200 text-red-800";
      case "admin":
        return "bg-purple-200 text-purple-800";
      case "support":
        return "bg-yellow-200 text-yellow-800";
      case "viewer":
        return "bg-gray-200 text-gray-800";
      default:
        // Fallback to the hash function for other groups
        const colors = [
          "bg-blue-200 text-blue-800",
          "bg-green-200 text-green-800",
          "bg-indigo-200 text-indigo-800",
          "bg-pink-200 text-pink-800",
        ];
        let hash = 0;
        for (let i = 0; i < groupName.length; i++) {
          hash = ((hash << 5) - hash + groupName.charCodeAt(i)) & 0xffffffff;
        }
        return colors[Math.abs(hash) % colors.length];
    }
  };
  return (
    <span className={`px-2 py-1 rounded text-xs ${getGroupColor(group)}`}>
      {group}
    </span>
  );
}

function StatusBadge({ status }: { status: string }) {
  const statusStyles: { [key: string]: string } = {
    // Explicitly type statusStyles
    active: "bg-green-500 text-gray-100",
    inactive: "bg-red-200 text-red-700",
  };

  return (
    <span
      className={`px-2 py-1 rounded text-xs ${
        statusStyles[status] || "bg-gray-100 text-gray-800"
      }`}
    >
      {status}
    </span>
  );
}

// function PasswordInput({
//   value,
//   onChange,
//   strength,
//   placeholder = "Password",
// }: {
//   value: string;
//   onChange: (password: string) => void;
//   strength: { length: boolean; uppercase: boolean; number: boolean };
//   placeholder?: string;
// }) {
//   return (
//     <div className="space-y-2">
//       <Input
//         type="password"
//         placeholder={placeholder}
//         value={value}
//         onChange={(e) => onChange(e.target.value)}
//       />
//       <div className="flex flex-wrap gap-2 text-sm">
//         {/* Added flex-wrap */}
//         <span className={strength.length ? "text-green-600" : "text-gray-500"}>
//           • 8+ characters
//         </span>
//         <span
//           className={strength.uppercase ? "text-green-600" : "text-gray-500"}
//         >
//           • Uppercase
//         </span>
//         <span className={strength.number ? "text-green-600" : "text-gray-500"}>
//           • Number
//         </span>
//       </div>
//     </div>
//   );
// }
