"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";

interface DepartmentFormProps {
  onSubmit: (packageData: any) => Promise<boolean>;
  onCancel?: () => void;
  initialData?: any;
  isEdit?: boolean;
}

export default function DepartmentForm({
  onSubmit,
  onCancel,
  initialData,
  isEdit = false,
}: DepartmentFormProps) {
  const [form, setForm] = useState({
    package_name: initialData?.package_name || "",
    upload: initialData?.upload || "",
    download: initialData?.download || "",
    status: initialData?.status || "Active",
    vlan: initialData?.vlan || "",
    role: initialData?.role || "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [nasVlan, setNasVlan] = useState([]);
  const [nasRole, setNasRole] = useState([]);

  useEffect(() => {
    setForm({
      package_name: initialData?.package_name || "",
      upload: initialData?.upload ?? "10",
      download: initialData?.download ?? "10",
      status: initialData?.status || "Active",
      vlan: initialData?.vlan || "",
      role: initialData?.role || "",
    });
    getNasVlan();
    getNasRole();
  }, [initialData]);

  const handleSpeedChange = (value) => {
    setForm(prev => ({
      ...prev,
      upload: value,
      download: value,
    }));
  };


  const getNasVlan = async () => {
    try {
      const response = await apiClient.get("/nas/vlan");
      setNasVlan(response?.data || []);
    } catch (error) {
      console.error("Failed to fetch NAS:", error);
      toast.error(error.message || "Failed to fetch NAS");
    }
  };

  const getNasRole = async () => {
    try {
      const response = await apiClient.get("/nas/role");
      setNasRole(response?.data || []);
    } catch (error) {
      console.error("Failed to fetch NAS:", error);
      toast.error(error.message || "Failed to fetch NAS");
    }
  };

  const isFormValid = () => {
    return form.package_name.trim() !== "";
  };

  const handleChange = (field: keyof typeof form, value: string) => {
    setForm((prevForm) => ({ ...prevForm, [field]: value }));
  };

  const getChanged = () => {
    const changed: Record<string, any> = { id: initialData?.id };
    for (const key in form) {
      if (
        Object.prototype.hasOwnProperty.call(form, key) &&
        form[key] !== initialData?.[key]
      ) {
        changed[key] = form[key];
      }
    }
    return changed;
  };

  const resetForm = () => {
    setForm({
      package_name: "",
      upload: "10",
      download: "10",
      status: "Active",
      vlan: "",
      role: "",
    });
  };

  const handleSubmit = async () => {
    if (!isFormValid() || isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    let payloadToSubmit;
    if (isEdit) {
      payloadToSubmit = getChanged();
    } else {
      payloadToSubmit = form;
    }

    const success = await onSubmit?.(payloadToSubmit);
    if (success) {
      resetForm();
    }

    setIsSubmitting(false);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-[37rem] w-full max-h-[90vh] flex flex-col">
        <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
          <h1 className="text-lg font-extrabold text-center">
            {isEdit ? (
              <>
                Edit Department{" "}
                <span className="text-blue-300">{form.package_name}</span>
              </>
            ) : (
              "Add Department"
            )}
          </h1>
        </div>
        <div className="flex-grow p-7 grid grid-cols-1 gap-x-5 gap-y-3 overflow-y-auto custom-scrollbar">
          <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Department Name *</Label>
              <Input
                type="text"
                value={form.package_name}
                onChange={(e) => handleChange("package_name", e.target.value)}
                placeholder="e.g. Management"
              />
            </div>

            <div className="space-y-2">
              <Label>VLAN</Label>
              <select
                className="w-full border px-3 py-2 rounded-md"
                value={form.vlan} // assume form.vlan stores the selected vlan's id
                onChange={(e) => handleChange("vlan", e.target.value)}
              >
                <option value="">Select vlan</option>
                {nasVlan.map((vlan) => (
                  <option key={vlan.id} value={vlan.id}>
                    {vlan.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label>Bandwidth *</Label>

              <input
                type="range"
                min={10}
                max={1000}
                step={5}
                value={form.upload}
                onChange={(e) => handleSpeedChange(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none "
                required
              />
              
            </div>
            <div className="space-y-2">
                {/* <Label> {'\u200B'} </Label> */}
              <div className="text-sm text-muted-foreground">{form.upload} Mbps</div>
              <input
                type="number"
                min={10}
                max={1000}
                step={1}
                value={form.upload}
                onChange={(e) => handleSpeedChange(Number(e.target.value))}
                className="w-full text-right border rounded px-2 py-1 text-sm"
              />
            </div>


            <div className="space-y-2">
              <Label>Role</Label>
              <select
                className="w-full border px-3 py-2 rounded-md"
                value={form.role} // assume form.role stores the selected role's id
                onChange={(e) => handleChange("role", e.target.value)}
              >
                <option value="">Select role</option>
                {nasRole.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label>Status *</Label>
              <div className="flex gap-4">
                {/* Active Button */}
                <button
                  type="button"
                  aria-pressed={form.status === "Active"}
                  className={`w-20 h-8 border-2 rounded-md flex items-center justify-center
        ${form.status === "Active"
                      ? "border-blue-600 bg-blue-600 text-white"
                      : "border-gray-300 bg-white text-black"
                    }
        hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "Active")}
                >
                  Active
                </button>
                <button
                  type="button"
                  aria-pressed={form.status === "Inactive"}
                  className={`w-20 h-8 border-2 rounded-md flex items-center justify-center
        ${form.status === "Inactive"
                      ? "border-blue-600 bg-blue-600 text-white"
                      : "border-gray-300 bg-white text-black"
                    }
        hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "Inactive")}
                >
                  Inactive
                </button>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-end sm:flex-row gap-3 pt-3 border-t border-gray-200 mt-3">
            {" "}
            {onCancel && (
              <Button
                onClick={onCancel}
                variant="outline"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              onClick={handleSubmit}
              className="bg-primary "
              disabled={!isFormValid() || isSubmitting}
            >
              {isSubmitting
                ? isEdit
                  ? "Updating..."
                  : "Submitting..."
                : isEdit
                  ? "Update"
                  : "Submit"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
