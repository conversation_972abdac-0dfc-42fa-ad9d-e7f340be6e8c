"use client";

import { useState, useEffect, useMemo } from "react";
import { Trash2 } from "@/components/icons/list";
import { UserTable, User } from "@/components/settings/users/user-management";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";
import { AddUserForm } from "@/components/settings/users/add-user";
import { useAuth } from "@/context/AuthContext";
import { EditUserForm } from "@/components/settings/users/edit-user";

export default function UserManagementPage() {
  const [userData, setUser] = useState<User[]>();
  const [search, setSearch] = useState("");
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User>();
  const [isEditOpen, setIsEditOpen] = useState(false);
  const { isRefreshed, setIsRefreshed } = useAuth();
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiClient.get("/user");
        // Backend returns: { success: true, message: "User details", data: [...] }
        // The apiClient response interceptor extracts the data property
        setUser(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch users:", error);
        toast.error(error.message || "Failed to fetch users");
      }
    };

    fetchUsers();
  }, [isRefreshed]);

  const handleAddSubmit = async (newUser) => {
    console.log("newUser payload being sent:", newUser);
    try {
      const res = await apiClient.post("/user", newUser);
      console.log(res?.message);
      toast.success(res?.message || "User created sucessfully");
      setIsAddOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.log("Failed to add user:", error);
      const errorMsg =
        error?.response?.data?.errors?.[0]?.msg || "Failed to add user";
      toast.error(errorMsg);
    }
  };
  const handleEditSubmit = async (user) => {
    const { id, ...payload } = user || {};
    try {
      const response = await apiClient.patch(`/user/${id}`, payload);
      // Backend returns: { message: "updated sucessfully" }
      toast.success("User Detail Edited Successfuly");
      setIsEditOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update user:", error);
      toast.error(error.message || "Failed to update user");
    }
  };
  const handleEditClick = (user) => {
    setIsEditOpen(true);
    setSelectedUser(user);
  };

  const handleDeleteUser = async (id: string) => {
    try {
      const response = await apiClient.delete(`/user/${id}`);
      // Backend returns: { message: "User has been deleted" }
      toast.success("User deleted sucessfully");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to delete user:", error);
      toast.error(error.message || "Failed to delete user");
    }
  };
  const filteredUsers = useMemo(() => {
    return (userData ?? []).filter(
      (user) =>
        user?.username?.toLowerCase().includes(search.toLowerCase()) ||
        user?.email?.toLowerCase().includes(search.toLowerCase())
    );
  }, [userData, search]);

  return (
    <div className="p-5 sm:p-5 space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          User Management
        </h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto ">
          <Input
            type="text"
            placeholder="Search users"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
          />
          <Button
            onClick={() => setIsAddOpen(true)}
            className="w-full bg-buttoncolor sm:w-auto rounded-full"
          >
            Add New User
          </Button>
        </div>
      </div>

      <UserTable
        users={filteredUsers}
        onDelete={handleDeleteUser}
        onEdit={handleEditClick}
      />
      {isAddOpen && (
        <AddUserForm
          onSubmit={handleAddSubmit}
          onClose={() => setIsAddOpen(false)}
        ></AddUserForm>
      )}
      {isEditOpen && (
        <EditUserForm
          user={selectedUser}
          onSubmit={handleEditSubmit}
          onClose={() => setIsEditOpen(false)}
        ></EditUserForm>
      )}
    </div>
  );
}

export function DeleteUserDialog({
  username,
  onDelete,
}: {
  username: string;
  onDelete: () => void;
}) {
  const [showConfirm, setShowConfirm] = useState(false);
  const [inputName, setInputName] = useState("");
  const [showError, setShowError] = useState(false);

  const handleDelete = () => {
    if (inputName === username) {
      onDelete();
      setShowConfirm(false);
    } else {
      toast.error("Username does not match");
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setShowConfirm(false);
        setInputName("");
        setShowError(false); // Reset error state on escape
      }
    };

    if (showConfirm) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showConfirm]);

  useEffect(() => {
    if (inputName.length > 0 && inputName !== username) {
      setShowError(true);
    } else {
      setShowError(false);
    }
  }, [inputName, username]);

  return (
    <>
      <Button
        onClick={() => {
          setShowConfirm(true);
          setInputName("");
        }}
        className="bg-red-500 hover:bg-red-600 text-white p-1 rounded h-7 w-7"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 z-50">
          <div className="bg-white p-7 rounded-md w-11/12 md:w-auto max-w-sm">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Deletion
            </h2>
            <p className="mb-2 text-center sm:text-left">
              Are you sure you want to delete the user {""}
              <strong>{username}</strong>?
            </p>
            <p className="mb-4 text-sm text-gray-600">
              Type the username to confirm:
            </p>
            <input
              className="border px-3 py-2 w-full mb-4 rounded-md"
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter username"
            />
            {showError && (
              <p className="text-red-500 text-sm mb-3">
                Username does not match
              </p>
            )}
            <div className="flex justify-between gap-2 flex-wrap">
              <button
                onClick={() => {
                  setShowConfirm(false);
                  setInputName("");
                  setShowError(false);
                }}
                className="px-4 py-2 bg-gray-300 rounded-md w-full sm:w-auto"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={inputName !== username}
                className={`px-4 py-2 rounded-md w-full sm:w-auto 
                ${
                  inputName !== username
                    ? "bg-red-300 cursor-not-allowed" // Disabled state styling
                    : "bg-red-600 text-white" // Enabled state styling
                }`}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
